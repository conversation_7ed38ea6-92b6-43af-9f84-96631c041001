package resolvers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

// ActivityCashbackResolver handles activity cashback related GraphQL operations
type ActivityCashbackResolver struct {
	service      activity_cashback.ActivityCashbackServiceInterface
	adminService activity_cashback.AdminServiceInterface
}

// NewActivityCashbackResolver creates a new ActivityCashbackResolver
func NewActivityCashbackResolver() *ActivityCashbackResolver {
	return &ActivityCashbackResolver{
		service:      activity_cashback.NewActivityCashbackService(),
		adminService: activity_cashback.NewAdminService(),
	}
}

// ActivityCashbackDashboard retrieves user dashboard for activity cashback
func (r *ActivityCashbackResolver) ActivityCashbackDashboard(ctx context.Context) (*gql_model.UserDashboardResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Initialize activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Initialize user if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to initialize user data",
		}, nil
	}

	// Get dashboard data
	dashboard, err := service.GetUserDashboard(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user dashboard", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to retrieve dashboard data",
		}, nil
	}

	// Convert to GraphQL model
	gqlDashboard := convertUserDashboardToGQL(dashboard)

	return &gql_model.UserDashboardResponse{
		Success: true,
		Message: "Dashboard data retrieved successfully",
		Data:    gqlDashboard,
	}, nil
}

// TaskCenter retrieves task center data
func (r *ActivityCashbackResolver) TaskCenter(ctx context.Context) (*gql_model.TaskCenterResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	taskCenter, err := service.GetTaskCenter(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: "Failed to retrieve task center data",
		}, nil
	}

	// Convert to GraphQL model
	gqlTaskCenter := convertTaskCenterToGQL(taskCenter)

	return &gql_model.TaskCenterResponse{
		Success: true,
		Message: "Task center data retrieved successfully",
		Data:    gqlTaskCenter,
	}, nil
}

// TaskListByType retrieves tasks organized by type for mobile UI
func (r *ActivityCashbackResolver) TaskListByType(ctx context.Context, filter *gql_model.TaskListFilter) (*gql_model.TaskListResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskListResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Get task center data
	taskCenter, err := service.GetTaskCenter(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center", zap.Error(err))
		return &gql_model.TaskListResponse{
			Success: false,
			Message: "Failed to retrieve task center data",
		}, nil
	}

	// Get user tier info for level summary
	userTierInfo, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info", zap.Error(err))
		// Continue without tier info
	}

	// Convert to organized task list by type
	taskListByType := convertToTaskListByType(taskCenter, userTierInfo, filter)

	return &gql_model.TaskListResponse{
		Success: true,
		Message: "Task list retrieved successfully",
		Data:    taskListByType,
	}, nil
}

// FilteredTaskList retrieves tasks with filtering
func (r *ActivityCashbackResolver) FilteredTaskList(ctx context.Context, filter gql_model.TaskListFilter) (*gql_model.TaskCenterResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	taskCenter, err := service.GetTaskCenter(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: "Failed to retrieve task center data",
		}, nil
	}

	// Apply filters
	filteredTaskCenter := applyTaskFilters(taskCenter, &filter)

	// Convert to GraphQL model
	gqlTaskCenter := convertTaskCenterToGQL(filteredTaskCenter)

	return &gql_model.TaskCenterResponse{
		Success: true,
		Message: "Filtered task list retrieved successfully",
		Data:    gqlTaskCenter,
	}, nil
}

// CompleteTask completes a task for the user
func (r *ActivityCashbackResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	taskUUID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Parse verification data
	var verificationData map[string]interface{}
	if input.VerificationData != nil {
		if err := json.Unmarshal([]byte(*input.VerificationData), &verificationData); err != nil {
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: "Invalid verification data format",
			}, nil
		}
	}

	// Get task to determine if it's a community task that requires pending
	task, err := service.GetTaskByID(ctx, taskUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get task: %s", err.Error()),
		}, nil
	}

	// Check if this is a community task that requires 2-minute wait
	// Use enum-based detection for better maintainability
	isCommunityTaskWithWait := task.TaskType == model.TaskTypeCommunity &&
		task.TaskIdentifier != nil &&
		model.RequiresTwoMinuteWait(*task.TaskIdentifier)

	if isCommunityTaskWithWait {
		// Check if user already has a pending task
		hasPending, err := service.HasPendingCommunityTask(ctx, userUUID, taskUUID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to check pending task: %s", err.Error()),
			}, nil
		}

		if hasPending {
			// Get existing pending task to return remaining time
			pendingTask, err := service.GetPendingCommunityTask(ctx, userUUID, taskUUID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get pending community task", zap.Error(err))
			}

			remainingTime := int(120) // Default 2 minutes
			var completionTime *time.Time
			if pendingTask != nil {
				remainingTime = int(pendingTask.GetRemainingWaitTime())
				completionTime = pendingTask.CompletionTime
			}

			return &gql_model.TaskCompletionResponse{
				Success:                  true,
				Message:                  "Task is already pending. Please wait for completion.",
				PointsAwarded:            0,
				TierUpgraded:             false,
				IsPending:                true,
				RemainingWaitTimeSeconds: &remainingTime,
				CompletionTime:           completionTime,
			}, nil
		}

		// Create pending community task
		pendingTask, err := service.CreatePendingCommunityTask(ctx, userUUID, taskUUID, verificationData)
		if err != nil {
			global.GVA_LOG.Error("Failed to create pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to create pending task: %s", err.Error()),
			}, nil
		}

		remainingTime := int(pendingTask.GetRemainingWaitTime())
		return &gql_model.TaskCompletionResponse{
			Success:                  true,
			Message:                  "Task started! Please wait 2 minutes for completion and points to be awarded.",
			PointsAwarded:            0, // Points will be awarded later
			TierUpgraded:             false,
			IsPending:                true,
			RemainingWaitTimeSeconds: &remainingTime,
			CompletionTime:           pendingTask.CompletionTime,
		}, nil
	}

	// For non-community tasks, proceed with normal completion
	// Get user tier info before completion
	tierInfoBefore, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info before task completion", zap.Error(err))
	}

	// Complete the task
	if err := service.CompleteTask(ctx, userUUID, taskUUID, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to complete task", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to complete task: %s", err.Error()),
		}, nil
	}

	pointsAwarded := task.Points

	// Check if tier was upgraded
	tierUpgraded := false
	newTierLevel := 0
	if tierInfoBefore != nil {
		tierInfoAfter, err := service.GetUserTierInfo(ctx, userUUID)
		if err == nil && tierInfoAfter.CurrentTier > tierInfoBefore.CurrentTier {
			tierUpgraded = true
			newTierLevel = tierInfoAfter.CurrentTier
		}
	}

	return &gql_model.TaskCompletionResponse{
		Success:                  true,
		Message:                  "Task completed successfully",
		PointsAwarded:            pointsAwarded,
		NewTierLevel:             &newTierLevel,
		TierUpgraded:             tierUpgraded,
		IsPending:                false,
		RemainingWaitTimeSeconds: nil,
		CompletionTime:           nil,
	}, nil
}

// ClaimTaskReward claims reward for a completed task
func (r *ActivityCashbackResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskClaimResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	taskUUID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Get task progress to determine points
	progress, err := service.GetTaskProgress(ctx, userUUID, taskUUID)
	if err != nil {
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: "Task progress not found",
		}, nil
	}

	if err := service.ClaimTaskReward(ctx, userUUID, taskUUID); err != nil {
		global.GVA_LOG.Error("Failed to claim task reward", zap.Error(err))
		return &gql_model.TaskClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to claim reward: %s", err.Error()),
		}, nil
	}

	return &gql_model.TaskClaimResponse{
		Success:       true,
		Message:       "Task reward claimed successfully",
		PointsClaimed: progress.PointsEarned,
	}, nil
}

// ClaimCashback claims cashback for the user
func (r *ActivityCashbackResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	amountUSD := decimal.NewFromFloat(input.AmountUsd)

	// For now, assume 1 SOL = 100 USD (this should come from price service)
	amountSOL := amountUSD.Div(decimal.NewFromInt(100))

	// Create claim
	claim, err := service.CreateClaim(ctx, userUUID, model.ClaimTypeTradingCashback, amountUSD, amountSOL, nil)
	if err != nil {
		global.GVA_LOG.Error("Failed to create cashback claim", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create claim: %s", err.Error()),
		}, nil
	}

	// Update user tier info to reflect claimed amount
	if err := service.ClaimCashback(ctx, userUUID, amountUSD); err != nil {
		global.GVA_LOG.Error("Failed to update user cashback", zap.Error(err))
		// Don't return error here as claim is already created
	}

	amountUSDFloat, _ := amountUSD.Float64()
	amountSOLFloat, _ := amountSOL.Float64()

	return &gql_model.CashbackClaimResponse{
		Success:   true,
		Message:   "Cashback claim created successfully",
		ClaimID:   claim.ID.String(),
		AmountUsd: amountUSDFloat,
		AmountSol: amountSOLFloat,
	}, nil
}

// RefreshTaskList refreshes the task list for the user
func (r *ActivityCashbackResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return false, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return false, fmt.Errorf("failed to initialize user: %s", err.Error())
	}

	if err := service.RefreshTaskList(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to refresh task list", zap.Error(err))
		return false, err
	}

	return true, nil
}

// Helper functions to convert models to GraphQL types

func convertUserDashboardToGQL(dashboard *activity_cashback.UserDashboard) *gql_model.UserDashboard {
	if dashboard == nil {
		return nil
	}

	gqlDashboard := &gql_model.UserDashboard{
		UserTierInfo:     convertUserTierInfoToGQL(dashboard.UserTierInfo),
		TierBenefit:      convertTierBenefitToGQL(dashboard.TierBenefit),
		PointsToNextTier: dashboard.PointsToNextTier,
		UserRank:         dashboard.UserRank,
	}

	if dashboard.NextTier != nil {
		gqlDashboard.NextTier = convertTierBenefitToGQL(dashboard.NextTier)
	}

	claimableCashback, _ := dashboard.ClaimableCashback.Float64()
	gqlDashboard.ClaimableCashback = claimableCashback

	for _, claim := range dashboard.RecentClaims {
		gqlDashboard.RecentClaims = append(gqlDashboard.RecentClaims, convertActivityCashbackClaimToGQL(&claim))
	}

	return gqlDashboard
}

func convertTaskCenterToGQL(taskCenter *activity_cashback.TaskCenter) *gql_model.TaskCenter {
	if taskCenter == nil {
		return nil
	}

	gqlTaskCenter := &gql_model.TaskCenter{
		CompletedToday:    taskCenter.CompletedToday,
		PointsEarnedToday: taskCenter.PointsEarnedToday,
	}

	for _, category := range taskCenter.Categories {
		gqlCategory := &gql_model.TaskCategoryWithTasks{
			Category: convertTaskCategoryToGQL(&category.Category),
		}

		for _, taskWithProgress := range category.Tasks {
			gqlTaskWithProgress := &gql_model.TaskWithProgress{
				Task: convertActivityTaskToGQL(&taskWithProgress.Task),
			}
			if taskWithProgress.Progress != nil {
				gqlTaskWithProgress.Progress = convertUserTaskProgressToGQL(taskWithProgress.Progress)
			}
			gqlCategory.Tasks = append(gqlCategory.Tasks, gqlTaskWithProgress)
		}

		gqlTaskCenter.Categories = append(gqlTaskCenter.Categories, gqlCategory)
	}

	for _, progress := range taskCenter.UserProgress {
		gqlTaskCenter.UserProgress = append(gqlTaskCenter.UserProgress, convertUserTaskProgressToGQL(&progress))
	}

	for _, streak := range taskCenter.StreakTasks {
		gqlTaskCenter.StreakTasks = append(gqlTaskCenter.StreakTasks, convertUserTaskProgressToGQL(&streak))
	}

	return gqlTaskCenter
}

func convertUserTierInfoToGQL(tierInfo *model.UserTierInfo) *gql_model.UserTierInfo {
	if tierInfo == nil {
		return nil
	}

	tradingVolume, _ := tierInfo.TradingVolumeUSD.Float64()
	cumulativeCashback, _ := tierInfo.CumulativeCashbackUSD.Float64()
	claimableCashback, _ := tierInfo.ClaimableCashbackUSD.Float64()
	claimedCashback, _ := tierInfo.ClaimedCashbackUSD.Float64()

	gqlTierInfo := &gql_model.UserTierInfo{
		UserID:                tierInfo.UserID.String(),
		CurrentTier:           tierInfo.CurrentTier,
		TotalPoints:           tierInfo.TotalPoints,
		PointsThisMonth:       tierInfo.PointsThisMonth,
		TradingVolumeUsd:      tradingVolume,
		ActiveDaysThisMonth:   tierInfo.ActiveDaysThisMonth,
		CumulativeCashbackUsd: cumulativeCashback,
		ClaimableCashbackUsd:  claimableCashback,
		ClaimedCashbackUsd:    claimedCashback,
		CreatedAt:             tierInfo.CreatedAt,
		UpdatedAt:             tierInfo.UpdatedAt,
	}

	if tierInfo.LastActivityDate != nil {
		gqlTierInfo.LastActivityDate = tierInfo.LastActivityDate
	}
	if tierInfo.TierUpgradedAt != nil {
		gqlTierInfo.TierUpgradedAt = tierInfo.TierUpgradedAt
	}
	if tierInfo.MonthlyResetAt != nil {
		gqlTierInfo.MonthlyResetAt = tierInfo.MonthlyResetAt
	}
	if tierInfo.TierBenefit != nil {
		gqlTierInfo.TierBenefit = convertTierBenefitToGQL(tierInfo.TierBenefit)
	}

	return gqlTierInfo
}

func convertTierBenefitToGQL(benefit *model.TierBenefit) *gql_model.TierBenefit {
	if benefit == nil {
		return nil
	}

	cashbackPercentage, _ := benefit.CashbackPercentage.Float64()

	gqlBenefit := &gql_model.TierBenefit{
		ID:                 strconv.Itoa(int(benefit.ID)),
		TierLevel:          benefit.TierLevel,
		TierName:           benefit.TierName,
		MinPoints:          benefit.MinPoints,
		CashbackPercentage: cashbackPercentage,
		IsActive:           benefit.IsActive,
		CreatedAt:          benefit.CreatedAt,
		UpdatedAt:          benefit.UpdatedAt,
	}

	if benefit.BenefitsDescription != nil {
		gqlBenefit.BenefitsDescription = benefit.BenefitsDescription
	}
	if benefit.TierColor != nil {
		gqlBenefit.TierColor = benefit.TierColor
	}
	if benefit.TierIcon != nil {
		gqlBenefit.TierIcon = benefit.TierIcon
	}

	return gqlBenefit
}

func convertTaskCategoryToGQL(category *model.TaskCategory) *gql_model.TaskCategory {
	if category == nil {
		return nil
	}

	gqlCategory := &gql_model.TaskCategory{
		ID:          strconv.Itoa(int(category.ID)),
		Name:        category.Name,
		DisplayName: category.DisplayName,
		SortOrder:   category.SortOrder,
		IsActive:    category.IsActive,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}

	if category.Description != nil {
		gqlCategory.Description = category.Description
	}
	if category.Icon != nil {
		gqlCategory.Icon = category.Icon
	}

	for _, task := range category.Tasks {
		gqlCategory.Tasks = append(gqlCategory.Tasks, convertActivityTaskToGQL(&task))
	}

	return gqlCategory
}

func convertActivityTaskToGQL(task *model.ActivityTask) *gql_model.ActivityTask {
	if task == nil {
		return nil
	}

	gqlTask := &gql_model.ActivityTask{
		ID:         task.ID.String(),
		CategoryID: strconv.Itoa(int(task.CategoryID)),
		Name:       task.Name,
		TaskType:   convertTaskTypeToGQL(task.TaskType),
		Frequency:  convertTaskFrequencyToGQL(task.Frequency),
		Points:     task.Points,
		IsActive:   task.IsActive,
		SortOrder:  task.SortOrder,
		CreatedAt:  task.CreatedAt,
		UpdatedAt:  task.UpdatedAt,
	}

	if task.Description != nil {
		gqlTask.Description = task.Description
	}
	if task.TaskIdentifier != nil {
		taskIdentifier := gql_model.TaskIdentifier(string(*task.TaskIdentifier))
		gqlTask.TaskIdentifier = &taskIdentifier
	}
	if task.MaxCompletions != nil {
		gqlTask.MaxCompletions = task.MaxCompletions
	}
	if task.ResetPeriod != nil {
		resetPeriod := string(*task.ResetPeriod)
		gqlTask.ResetPeriod = &resetPeriod
	}
	if task.Conditions != nil {
		conditionsJSON, _ := json.Marshal(task.Conditions)
		conditionsStr := string(conditionsJSON)
		gqlTask.Conditions = &conditionsStr
	}
	if task.ActionTarget != nil {
		gqlTask.ActionTarget = task.ActionTarget
	}
	if task.VerificationMethod != nil {
		verificationMethod := string(*task.VerificationMethod)
		gqlTask.VerificationMethod = &verificationMethod
	}
	if task.ExternalLink != nil {
		gqlTask.ExternalLink = task.ExternalLink
	}
	if task.StartDate != nil {
		gqlTask.StartDate = utils.TimeToTimestamp(task.StartDate)
	}
	if task.EndDate != nil {
		gqlTask.EndDate = utils.TimeToTimestamp(task.EndDate)
	}
	if task.Category.ID != 0 {
		gqlTask.Category = convertTaskCategoryToGQL(&task.Category)
	}

	return gqlTask
}

func convertUserTaskProgressToGQL(progress *model.UserTaskProgress) *gql_model.UserTaskProgress {
	if progress == nil {
		return nil
	}

	gqlProgress := &gql_model.UserTaskProgress{
		ID:                 progress.ID.String(),
		UserID:             progress.UserID.String(),
		TaskID:             progress.TaskID.String(),
		Status:             convertTaskStatusToGQL(progress.Status),
		ProgressValue:      progress.ProgressValue,
		CompletionCount:    progress.CompletionCount,
		PointsEarned:       progress.PointsEarned,
		StreakCount:        progress.StreakCount,
		CreatedAt:          progress.CreatedAt,
		UpdatedAt:          progress.UpdatedAt,
		ProgressPercentage: progress.GetProgressPercentage(),
		CanBeClaimed:       progress.CanBeClaimed(),
	}

	if progress.TargetValue != nil {
		gqlProgress.TargetValue = progress.TargetValue
	}
	if progress.LastCompletedAt != nil {
		gqlProgress.LastCompletedAt = progress.LastCompletedAt
	}
	if progress.LastResetAt != nil {
		gqlProgress.LastResetAt = progress.LastResetAt
	}
	if progress.Metadata != nil {
		metadataJSON, _ := json.Marshal(progress.Metadata)
		metadataStr := string(metadataJSON)
		gqlProgress.Metadata = &metadataStr
	}
	if progress.Task.ID != uuid.Nil {
		gqlProgress.Task = convertActivityTaskToGQL(&progress.Task)
	}

	return gqlProgress
}

// convertToTaskListByType converts task center data to organized task list by type
func convertToTaskListByType(taskCenter *activity_cashback.TaskCenter, userTierInfo *model.UserTierInfo, filter *gql_model.TaskListFilter) *gql_model.TaskListByType {
	// Initialize task type groups
	dailyTasks := &gql_model.TaskTypeGroup{
		TaskType:    gql_model.TaskTypeDaily,
		DisplayName: "Daily Tasks",
		Tasks:       []*gql_model.TaskWithProgressAndStatus{},
	}

	communityTasks := &gql_model.TaskTypeGroup{
		TaskType:    gql_model.TaskTypeCommunity,
		DisplayName: "Community Tasks",
		Tasks:       []*gql_model.TaskWithProgressAndStatus{},
	}

	tradingTasks := &gql_model.TaskTypeGroup{
		TaskType:    gql_model.TaskTypeTrading,
		DisplayName: "Trading Tasks",
		Tasks:       []*gql_model.TaskWithProgressAndStatus{},
	}

	// Process tasks by category
	for _, categoryWithTasks := range taskCenter.Categories {
		for _, taskWithProgress := range categoryWithTasks.Tasks {
			task := &taskWithProgress.Task
			progress := taskWithProgress.Progress

			// Apply filters if specified
			if filter != nil {
				if filter.TaskType != nil && task.TaskType != model.TaskType(*filter.TaskType) {
					continue
				}
				if filter.IncludeInactive != nil && !*filter.IncludeInactive && !task.IsActive {
					continue
				}
			}

			// Convert to TaskWithProgressAndStatus
			taskWithStatus := convertToTaskWithProgressAndStatus(task, progress)

			// Group by task type
			switch task.TaskType {
			case model.TaskTypeDaily:
				dailyTasks.Tasks = append(dailyTasks.Tasks, taskWithStatus)
				dailyTasks.TotalPointsAvailable += task.Points
				if progress != nil && progress.Status == model.TaskStatusCompleted {
					dailyTasks.CompletedCount++
					dailyTasks.TotalPointsEarned += progress.PointsEarned
				}
				dailyTasks.TotalCount++

			case model.TaskTypeCommunity:
				communityTasks.Tasks = append(communityTasks.Tasks, taskWithStatus)
				communityTasks.TotalPointsAvailable += task.Points
				if progress != nil && progress.Status == model.TaskStatusCompleted {
					communityTasks.CompletedCount++
					communityTasks.TotalPointsEarned += progress.PointsEarned
				}
				communityTasks.TotalCount++

			case model.TaskTypeTrading:
				tradingTasks.Tasks = append(tradingTasks.Tasks, taskWithStatus)
				tradingTasks.TotalPointsAvailable += task.Points
				if progress != nil && progress.Status == model.TaskStatusCompleted {
					tradingTasks.CompletedCount++
					tradingTasks.TotalPointsEarned += progress.PointsEarned
				}
				tradingTasks.TotalCount++
			}
		}
	}

	// Create user level summary
	userLevelSummary := createUserLevelSummary(userTierInfo)

	// Create daily task stats
	dailyStats := createDailyTaskStats(taskCenter, dailyTasks)

	return &gql_model.TaskListByType{
		DailyTasks:     dailyTasks,
		CommunityTasks: communityTasks,
		TradingTasks:   tradingTasks,
		UserLevelInfo:  userLevelSummary,
		TodayStats:     dailyStats,
	}
}

// convertToTaskWithProgressAndStatus converts task and progress to TaskWithProgressAndStatus
func convertToTaskWithProgressAndStatus(task *model.ActivityTask, progress *model.UserTaskProgress) *gql_model.TaskWithProgressAndStatus {
	// Determine completion status
	completionStatus := gql_model.TaskCompletionStatusNotCompleted
	if progress != nil {
		switch progress.Status {
		case model.TaskStatusCompleted:
			completionStatus = gql_model.TaskCompletionStatusCompleted
		case model.TaskStatusInProgress:
			completionStatus = gql_model.TaskCompletionStatusInProgress
		case model.TaskStatusClaimed:
			completionStatus = gql_model.TaskCompletionStatusCompleted
		default:
			completionStatus = gql_model.TaskCompletionStatusNotCompleted
		}
	}

	// Calculate progress percentage
	progressPercentage := 0.0
	if progress != nil {
		progressPercentage = progress.GetProgressPercentage()
	}

	// Determine if task is claimable
	isClaimable := progress != nil && progress.CanBeClaimed()

	// Determine if task can be completed (not already completed and active)
	canComplete := task.IsActive && (progress == nil || progress.Status != model.TaskStatusCompleted)

	// Calculate remaining attempts
	remainingAttempts := 0
	if task.MaxCompletions != nil {
		if progress != nil {
			remainingAttempts = *task.MaxCompletions - progress.CompletionCount
		} else {
			remainingAttempts = *task.MaxCompletions
		}
		if remainingAttempts < 0 {
			remainingAttempts = 0
		}
	} else {
		remainingAttempts = -1 // Unlimited
	}

	return &gql_model.TaskWithProgressAndStatus{
		Task:                    convertActivityTaskToGQL(task),
		Progress:                convertUserTaskProgressToGQL(progress),
		CompletionStatus:        completionStatus,
		IsClaimable:             isClaimable,
		CanComplete:             canComplete,
		NextResetTime:           calculateNextResetTime(task, progress),
		RemainingAttempts:       &remainingAttempts,
		ProgressPercentage:      progressPercentage,
		EstimatedCompletionTime: calculateEstimatedCompletionTime(task, progress),
	}
}

// calculateNextResetTime calculates when the task will reset next
func calculateNextResetTime(task *model.ActivityTask, progress *model.UserTaskProgress) *time.Time {
	if task.ResetPeriod == nil {
		return nil
	}

	now := time.Now()
	var nextReset time.Time

	switch *task.ResetPeriod {
	case model.ResetDaily:
		nextReset = now.Truncate(24 * time.Hour).Add(24 * time.Hour)
	case model.ResetWeekly:
		// Next Monday
		daysUntilMonday := (7 - int(now.Weekday()) + 1) % 7
		if daysUntilMonday == 0 {
			daysUntilMonday = 7
		}
		nextReset = now.Truncate(24*time.Hour).AddDate(0, 0, daysUntilMonday)
	case model.ResetMonthly:
		// First day of next month
		nextReset = time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
	default:
		return nil
	}

	return &nextReset
}

// calculateEstimatedCompletionTime estimates when the task might be completed
func calculateEstimatedCompletionTime(task *model.ActivityTask, progress *model.UserTaskProgress) *time.Time {
	// For now, return nil as this would require complex prediction logic
	// In the future, this could analyze user patterns and task difficulty
	return nil
}

// createUserLevelSummary creates user level summary from tier info
func createUserLevelSummary(userTierInfo *model.UserTierInfo) *gql_model.UserLevelSummary {
	if userTierInfo == nil {
		return &gql_model.UserLevelSummary{
			CurrentLevel:        1,
			CurrentLevelName:    "Bronze",
			ProgressToNextLevel: 0.0,
			PointsToNextLevel:   100,
			TotalPoints:         0,
			LevelBenefits:       []string{"Basic trading access"},
			NextLevelBenefits:   []string{"Reduced fees", "Priority support"},
		}
	}

	// Calculate progress to next level
	progressToNextLevel := 0.0
	pointsToNextLevel := 0

	// This would need to be calculated based on tier benefit system
	// For now, using placeholder logic
	nextTierPoints := (userTierInfo.CurrentTier + 1) * 1000 // Example calculation
	if nextTierPoints > userTierInfo.TotalPoints {
		pointsToNextLevel = nextTierPoints - userTierInfo.TotalPoints
		progressToNextLevel = float64(userTierInfo.TotalPoints%1000) / 1000.0 * 100
	}

	levelName := fmt.Sprintf("Level %d", userTierInfo.CurrentTier)
	switch userTierInfo.CurrentTier {
	case 1:
		levelName = "Bronze"
	case 2:
		levelName = "Silver"
	case 3:
		levelName = "Gold"
	case 4:
		levelName = "Platinum"
	case 5:
		levelName = "Diamond"
	}

	return &gql_model.UserLevelSummary{
		CurrentLevel:        userTierInfo.CurrentTier,
		CurrentLevelName:    levelName,
		ProgressToNextLevel: progressToNextLevel,
		PointsToNextLevel:   pointsToNextLevel,
		TotalPoints:         userTierInfo.TotalPoints,
		LevelBenefits:       []string{"Current tier benefits"},
		NextLevelBenefits:   []string{"Next tier benefits"},
	}
}

// createDailyTaskStats creates daily task statistics
func createDailyTaskStats(taskCenter *activity_cashback.TaskCenter, dailyTasks *gql_model.TaskTypeGroup) *gql_model.DailyTaskStats {
	// Calculate streak count from task center streak tasks
	streakCount := 0
	for _, streakTask := range taskCenter.StreakTasks {
		if streakTask.StreakCount > streakCount {
			streakCount = streakTask.StreakCount
		}
	}

	// Check if user can claim daily bonus (example logic)
	canClaimDailyBonus := dailyTasks.CompletedCount >= dailyTasks.TotalCount && dailyTasks.TotalCount > 0

	return &gql_model.DailyTaskStats{
		CompletedToday:     taskCenter.CompletedToday,
		TotalDailyTasks:    dailyTasks.TotalCount,
		PointsEarnedToday:  taskCenter.PointsEarnedToday,
		StreakCount:        streakCount,
		CanClaimDailyBonus: canClaimDailyBonus,
	}
}

// applyTaskFilters applies filters to task center data
func applyTaskFilters(taskCenter *activity_cashback.TaskCenter, filter *gql_model.TaskListFilter) *activity_cashback.TaskCenter {
	if filter == nil {
		return taskCenter
	}

	filteredCategories := []activity_cashback.TaskCategoryWithTasks{}

	for _, categoryWithTasks := range taskCenter.Categories {
		filteredTasks := []activity_cashback.TaskWithProgress{}

		for _, taskWithProgress := range categoryWithTasks.Tasks {
			task := &taskWithProgress.Task
			progress := taskWithProgress.Progress

			// Apply task type filter
			if filter.TaskType != nil && task.TaskType != model.TaskType(*filter.TaskType) {
				continue
			}

			// Apply category name filter
			if filter.CategoryName != nil && categoryWithTasks.Category.Name != *filter.CategoryName {
				continue
			}

			// Apply completion status filter
			if filter.CompletionStatus != nil {
				switch *filter.CompletionStatus {
				case gql_model.TaskCompletionStatusCompleted:
					if progress == nil || progress.Status != model.TaskStatusCompleted {
						continue
					}
				case gql_model.TaskCompletionStatusNotCompleted:
					if progress != nil && progress.Status == model.TaskStatusCompleted {
						continue
					}
				case gql_model.TaskCompletionStatusInProgress:
					if progress == nil || progress.Status != model.TaskStatusInProgress {
						continue
					}
				case gql_model.TaskCompletionStatusClaimable:
					if progress == nil || !progress.CanBeClaimed() {
						continue
					}
				}
			}

			// Apply active filter
			if filter.IncludeInactive != nil && !*filter.IncludeInactive && !task.IsActive {
				continue
			}

			filteredTasks = append(filteredTasks, taskWithProgress)
		}

		if len(filteredTasks) > 0 {
			filteredCategories = append(filteredCategories, activity_cashback.TaskCategoryWithTasks{
				Category: categoryWithTasks.Category,
				Tasks:    filteredTasks,
			})
		}
	}

	return &activity_cashback.TaskCenter{
		Categories:        filteredCategories,
		UserProgress:      taskCenter.UserProgress,
		CompletedToday:    taskCenter.CompletedToday,
		PointsEarnedToday: taskCenter.PointsEarnedToday,
		StreakTasks:       taskCenter.StreakTasks,
	}
}

func convertActivityCashbackClaimToGQL(claim *model.ActivityCashbackClaim) *gql_model.ActivityCashbackClaim {
	if claim == nil {
		return nil
	}

	totalAmountUSD, _ := claim.TotalAmountUSD.Float64()
	totalAmountSOL, _ := claim.TotalAmountSOL.Float64()

	gqlClaim := &gql_model.ActivityCashbackClaim{
		ID:             claim.ID.String(),
		UserID:         claim.UserID.String(),
		ClaimType:      convertClaimTypeToGQL(claim.ClaimType),
		TotalAmountUsd: totalAmountUSD,
		TotalAmountSol: totalAmountSOL,
		Status:         convertClaimStatusToGQL(claim.Status),
		ClaimedAt:      claim.ClaimedAt,
		CreatedAt:      claim.CreatedAt,
		UpdatedAt:      claim.UpdatedAt,
	}

	if claim.TransactionHash != nil {
		gqlClaim.TransactionHash = claim.TransactionHash
	}
	if claim.ProcessedAt != nil {
		gqlClaim.ProcessedAt = claim.ProcessedAt
	}
	if claim.Metadata != nil {
		metadataJSON, _ := json.Marshal(claim.Metadata)
		metadataStr := string(metadataJSON)
		gqlClaim.Metadata = &metadataStr
	}

	return gqlClaim
}

// Enum conversion functions
func convertTaskTypeToGQL(taskType model.TaskType) gql_model.TaskType {
	switch taskType {
	case model.TaskTypeDaily:
		return gql_model.TaskTypeDaily
	case model.TaskTypeCommunity:
		return gql_model.TaskTypeCommunity
	case model.TaskTypeTrading:
		return gql_model.TaskTypeTrading
	default:
		return gql_model.TaskTypeDaily
	}
}

func convertTaskFrequencyToGQL(frequency model.TaskFrequency) gql_model.TaskFrequency {
	switch frequency {
	case model.FrequencyDaily:
		return gql_model.TaskFrequencyDaily
	case model.FrequencyOneTime:
		return gql_model.TaskFrequencyOneTime
	case model.FrequencyUnlimited:
		return gql_model.TaskFrequencyUnlimited
	case model.FrequencyProgressive:
		return gql_model.TaskFrequencyProgressive
	case model.FrequencyManual:
		return gql_model.TaskFrequencyManual
	default:
		return gql_model.TaskFrequencyDaily
	}
}

func convertTaskStatusToGQL(status model.TaskStatus) gql_model.TaskStatus {
	switch status {
	case model.TaskStatusNotStarted:
		return gql_model.TaskStatusNotStarted
	case model.TaskStatusInProgress:
		return gql_model.TaskStatusInProgress
	case model.TaskStatusCompleted:
		return gql_model.TaskStatusCompleted
	case model.TaskStatusClaimed:
		return gql_model.TaskStatusClaimed
	case model.TaskStatusExpired:
		return gql_model.TaskStatusExpired
	default:
		return gql_model.TaskStatusNotStarted
	}
}

func convertClaimTypeToGQL(claimType model.ClaimType) gql_model.ClaimType {
	switch claimType {
	case model.ClaimTypeTradingCashback:
		return gql_model.ClaimTypeTradingCashback
	case model.ClaimTypeTaskReward:
		return gql_model.ClaimTypeTaskReward
	case model.ClaimTypeTierBonus:
		return gql_model.ClaimTypeTierBonus
	case model.ClaimTypeReferralBonus:
		return gql_model.ClaimTypeReferralBonus
	default:
		return gql_model.ClaimTypeTradingCashback
	}
}

func convertClaimStatusToGQL(status model.ClaimStatus) gql_model.ClaimStatus {
	switch status {
	case model.ClaimStatusPending:
		return gql_model.ClaimStatusPending
	case model.ClaimStatusProcessing:
		return gql_model.ClaimStatusProcessing
	case model.ClaimStatusCompleted:
		return gql_model.ClaimStatusCompleted
	case model.ClaimStatusFailed:
		return gql_model.ClaimStatusFailed
	default:
		return gql_model.ClaimStatusPending
	}
}
